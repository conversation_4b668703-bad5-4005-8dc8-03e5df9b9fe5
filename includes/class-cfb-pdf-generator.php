<?php
/**
 * CFB Calculator PDF Generator
 * Handles PDF invoice generation using TCPDF
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_PDF_Generator {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_cfb_generate_pdf', array($this, 'ajax_generate_pdf'));
        add_action('wp_ajax_nopriv_cfb_generate_pdf', array($this, 'ajax_generate_pdf'));
        
        // Load TCPDF library
        $this->load_tcpdf();
    }
    
    /**
     * Load TCPDF library
     */
    private function load_tcpdf() {
        if (!class_exists('TCPDF')) {
            // Load Composer autoloader first
            $autoloader_path = CFB_CALCULATOR_PLUGIN_PATH . 'vendor/autoload.php';
            if (file_exists($autoloader_path)) {
                require_once $autoloader_path;
            }

            // If still not available, check manual installation paths
            if (!class_exists('TCPDF')) {
                $tcpdf_paths = array(
                    CFB_CALCULATOR_PLUGIN_PATH . 'vendor/tecnickcom/tcpdf/tcpdf.php',
                    CFB_CALCULATOR_PLUGIN_PATH . 'lib/tcpdf/tcpdf.php',
                    ABSPATH . 'wp-content/plugins/tcpdf/tcpdf.php'
                );

                foreach ($tcpdf_paths as $path) {
                    if (file_exists($path)) {
                        require_once $path;
                        break;
                    }
                }
            }

            // If TCPDF is still not found, we'll create a simple fallback
            if (!class_exists('TCPDF')) {
                $this->create_tcpdf_fallback();
            }
        }
    }
    
    /**
     * Create TCPDF fallback for basic PDF generation
     */
    private function create_tcpdf_fallback() {
        // For now, we'll use WordPress's built-in functionality
        // In production, you should install TCPDF properly
    }
    
    /**
     * AJAX generate PDF handler
     */
    public function ajax_generate_pdf() {
        // Check nonce - use the same nonce as calculator for frontend compatibility
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'cfb_calculator_nonce')) {
            wp_send_json_error(__('Security check failed', 'cfb-calculator'));
        }

        $invoice_id = intval($_POST['invoice_id']);
        
        if (!$invoice_id) {
            wp_send_json_error(__('Invalid invoice ID', 'cfb-calculator'));
        }
        
        $invoice = $this->get_invoice_data($invoice_id);
        
        if (!$invoice) {
            wp_send_json_error(__('Invoice not found', 'cfb-calculator'));
        }
        
        try {
            $pdf_path = $this->generate_invoice_pdf($invoice);
            
            if ($pdf_path) {
                // Update invoice with PDF path
                global $wpdb;
                $wpdb->update(
                    $wpdb->prefix . 'cfb_invoices',
                    array('pdf_path' => $pdf_path),
                    array('id' => $invoice_id),
                    array('%s'),
                    array('%d')
                );
                
                wp_send_json_success(array(
                    'message' => __('PDF generated successfully', 'cfb-calculator'),
                    'pdf_url' => $this->get_pdf_url($pdf_path)
                ));
            } else {
                wp_send_json_error(__('Failed to generate PDF', 'cfb-calculator'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('PDF generation error: ', 'cfb-calculator') . $e->getMessage());
        }
    }
    
    /**
     * Get invoice data
     */
    private function get_invoice_data($invoice_id) {
        global $wpdb;
        
        $invoice = $wpdb->get_row($wpdb->prepare("
            SELECT i.*, f.name as form_name
            FROM {$wpdb->prefix}cfb_invoices i
            LEFT JOIN {$wpdb->prefix}cfb_forms f ON i.form_id = f.id
            WHERE i.id = %d
        ", $invoice_id));
        
        if ($invoice) {
            // Get invoice items
            $invoice->items = $wpdb->get_results($wpdb->prepare("
                SELECT * FROM {$wpdb->prefix}cfb_invoice_items
                WHERE invoice_id = %d
                ORDER BY sort_order
            ", $invoice_id));
        }
        
        return $invoice;
    }
    
    /**
     * Generate invoice PDF
     */
    public function generate_invoice_pdf($invoice) {
        if (!class_exists('TCPDF')) {
            return $this->generate_simple_pdf($invoice);
        }
        
        // Get PDF template
        $template = get_option('cfb_pdf_template', 'modern');
        
        switch ($template) {
            case 'classic':
                return $this->generate_classic_pdf($invoice);
            case 'minimal':
                return $this->generate_minimal_pdf($invoice);
            case 'modern':
            default:
                return $this->generate_modern_pdf($invoice);
        }
    }
    
    /**
     * Generate modern PDF template
     */
    private function generate_modern_pdf($invoice) {
        // Get PDF settings
        $font_family = get_option('cfb_pdf_font_family', 'helvetica');
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);

        // Create new PDF document with RTL support if needed
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Set document information
        $pdf->SetCreator('CFB Calculator');
        $pdf->SetAuthor(get_option('cfb_company_name', get_bloginfo('name')));
        $pdf->SetTitle('Invoice ' . $invoice->invoice_number);
        $pdf->SetSubject('Invoice');

        // Remove default header/footer
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Set RTL if enabled
        if ($rtl_support) {
            $pdf->setRTL(true);
        }

        // Set margins
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetAutoPageBreak(TRUE, 20);

        // Add a page
        $pdf->AddPage();

        // Set font with Unicode support for RTL
        if ($rtl_support && in_array($font_family, ['dejavusans', 'helvetica'])) {
            $pdf->SetFont('dejavusans', '', $font_size);
        } else {
            $pdf->SetFont($font_family, '', $font_size);
        }
        
        // Company logo and info
        $this->add_company_header($pdf, $invoice);
        
        // Invoice details
        $this->add_invoice_details($pdf, $invoice);
        
        // Customer details
        $this->add_customer_details($pdf, $invoice);
        
        // Invoice items table
        $this->add_invoice_items_table($pdf, $invoice);
        
        // Totals
        $this->add_invoice_totals($pdf, $invoice);
        
        // Footer
        $this->add_invoice_footer($pdf, $invoice);
        
        // Save PDF
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';
        
        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }
        
        $filename = 'invoice-' . $invoice->invoice_number . '.pdf';
        $filepath = $pdf_dir . $filename;
        
        $pdf->Output($filepath, 'F');
        
        return 'cfb-invoices/' . $filename;
    }
    
    /**
     * Add company header to PDF
     */
    private function add_company_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));
        $company_website = get_option('cfb_company_website', get_site_url());
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Get color scheme colors
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Company logo
        $logo_url = get_option('cfb_company_logo', '');
        if ($logo_url) {
            // Convert URL to local path if it's a local file
            $logo_path = $this->url_to_path($logo_url);

            if ($logo_path && file_exists($logo_path)) {
                $logo_x = $rtl_support ? 150 : 20;
                $pdf->Image($logo_path, $logo_x, 20, 40, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                $pdf->SetY(35);
            }
        }

        // Company name with color scheme
        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size + 6);
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $align = $rtl_support ? 'L' : 'R';
        $pdf->Cell(0, 8, $company_name, 0, 1, $align);

        // Company details
        $pdf->SetFont($pdf->getFontFamily(), '', $font_size);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        if ($company_address) {
            $pdf->Cell(0, 5, $company_address, 0, 1, $align);
        }
        if ($company_phone) {
            $phone_label = $rtl_support ? 'تلفن: ' : 'Phone: ';
            $pdf->Cell(0, 5, $phone_label . $company_phone, 0, 1, $align);
        }
        if ($company_email) {
            $email_label = $rtl_support ? 'ایمیل: ' : 'Email: ';
            $pdf->Cell(0, 5, $email_label . $company_email, 0, 1, $align);
        }
        if ($company_website) {
            $website_label = $rtl_support ? 'وب‌سایت: ' : 'Website: ';
            $pdf->Cell(0, 5, $website_label . $company_website, 0, 1, $align);
        }

        $pdf->Ln(10);
    }

    /**
     * Get color scheme colors
     */
    private function get_color_scheme_colors($scheme) {
        $colors = array();

        switch ($scheme) {
            case 'blue':
                $colors['primary'] = array('r' => 0, 'g' => 115, 'b' => 170);
                $colors['secondary'] = array('r' => 102, 'g' => 126, 'b' => 234);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
            case 'green':
                $colors['primary'] = array('r' => 86, 'g' => 171, 'b' => 47);
                $colors['secondary'] = array('r' => 168, 'g' => 230, 'b' => 207);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
            case 'gray':
                $colors['primary'] = array('r' => 44, 'g' => 62, 'b' => 80);
                $colors['secondary'] = array('r' => 189, 'g' => 195, 'b' => 199);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
            default:
                $colors['primary'] = array('r' => 0, 'g' => 115, 'b' => 170);
                $colors['secondary'] = array('r' => 102, 'g' => 102, 'b' => 102);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
        }

        return $colors;
    }

    /**
     * Convert URL to local file path
     */
    private function url_to_path($url) {
        $upload_dir = wp_upload_dir();

        // Check if it's a local URL
        if (strpos($url, $upload_dir['baseurl']) === 0) {
            return str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);
        }

        // If it's an external URL, try to download it temporarily
        $temp_file = download_url($url);
        if (!is_wp_error($temp_file)) {
            return $temp_file;
        }

        return false;
    }
    
    /**
     * Add invoice details to PDF
     */
    private function add_invoice_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Invoice title
        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size + 14);
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $invoice_title = $rtl_support ? 'فاکتور' : 'INVOICE';
        $align = $rtl_support ? 'R' : 'L';
        $pdf->Cell(0, 12, $invoice_title, 0, 1, $align);

        $pdf->SetFont($pdf->getFontFamily(), '', $font_size);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        // Invoice number and date
        $invoice_number_label = $rtl_support ? 'شماره فاکتور:' : 'Invoice Number:';
        $invoice_date_label = $rtl_support ? 'تاریخ فاکتور:' : 'Invoice Date:';

        $pdf->Cell(40, 6, $invoice_number_label, 0, 0, $align);
        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
        $pdf->Cell(0, 6, $invoice->invoice_number, 0, 1, $align);

        $pdf->SetFont($pdf->getFontFamily(), '', $font_size);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->Cell(40, 6, $invoice_date_label, 0, 0, $align);
        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        // Format date based on RTL support
        $date_format = $rtl_support ? 'Y/m/d' : 'F j, Y';
        $formatted_date = date($date_format, strtotime($invoice->created_at));
        $pdf->Cell(0, 6, $formatted_date, 0, 1, $align);

        $pdf->Ln(5);
    }
    
    /**
     * Add customer details to PDF
     */
    private function add_customer_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size + 2);
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

        $bill_to_label = $rtl_support ? 'صورتحساب برای:' : 'Bill To:';
        $align = $rtl_support ? 'R' : 'L';
        $pdf->Cell(0, 8, $bill_to_label, 0, 1, $align);

        $pdf->SetFont($pdf->getFontFamily(), '', $font_size);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        $pdf->Cell(0, 5, $invoice->customer_name, 0, 1, $align);
        if ($invoice->customer_email) {
            $pdf->Cell(0, 5, $invoice->customer_email, 0, 1, $align);
        }
        if ($invoice->customer_phone) {
            $pdf->Cell(0, 5, $invoice->customer_phone, 0, 1, $align);
        }
        if ($invoice->customer_address) {
            $pdf->MultiCell(0, 5, $invoice->customer_address, 0, $align);
        }

        $pdf->Ln(10);
    }
    
    /**
     * Add invoice items table to PDF
     */
    private function add_invoice_items_table($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Table header
        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
        $pdf->SetFillColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->SetTextColor(255, 255, 255);

        // RTL table headers
        if ($rtl_support) {
            $headers = array(
                'جمع کل' => 35,
                'قیمت واحد' => 30,
                'تعداد' => 25,
                'شرح' => 80
            );
            $aligns = array('R', 'R', 'C', 'R');
        } else {
            $headers = array(
                'Description' => 80,
                'Quantity' => 25,
                'Unit Price' => 30,
                'Total' => 35
            );
            $aligns = array('L', 'C', 'R', 'R');
        }

        $i = 0;
        foreach ($headers as $header => $width) {
            $pdf->Cell($width, 8, $header, 1, 0, $aligns[$i], true);
            $i++;
        }
        $pdf->Ln();

        // Table content
        $pdf->SetFont($pdf->getFontFamily(), '', $font_size - 1);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        if (!empty($invoice->items)) {
            foreach ($invoice->items as $item) {
                if ($rtl_support) {
                    $pdf->Cell(35, 6, $this->format_currency($item->total_price), 1, 0, 'R');
                    $pdf->Cell(30, 6, $this->format_currency($item->unit_price), 1, 0, 'R');
                    $pdf->Cell(25, 6, number_format($item->quantity, 2), 1, 0, 'C');
                    $pdf->Cell(80, 6, $item->item_name, 1, 1, 'R');
                } else {
                    $pdf->Cell(80, 6, $item->item_name, 1, 0, 'L');
                    $pdf->Cell(25, 6, number_format($item->quantity, 2), 1, 0, 'C');
                    $pdf->Cell(30, 6, $this->format_currency($item->unit_price), 1, 0, 'R');
                    $pdf->Cell(35, 6, $this->format_currency($item->total_price), 1, 1, 'R');
                }
            }
        } else {
            // If no items, show the form calculation as a single item
            $calculation_label = $rtl_support ? 'محاسبه ' . $invoice->form_name : $invoice->form_name . ' Calculation';

            if ($rtl_support) {
                $pdf->Cell(35, 6, $this->format_currency($invoice->subtotal), 1, 0, 'R');
                $pdf->Cell(30, 6, $this->format_currency($invoice->subtotal), 1, 0, 'R');
                $pdf->Cell(25, 6, '1', 1, 0, 'C');
                $pdf->Cell(80, 6, $calculation_label, 1, 1, 'R');
            } else {
                $pdf->Cell(80, 6, $calculation_label, 1, 0, 'L');
                $pdf->Cell(25, 6, '1', 1, 0, 'C');
                $pdf->Cell(30, 6, $this->format_currency($invoice->subtotal), 1, 0, 'R');
                $pdf->Cell(35, 6, $this->format_currency($invoice->subtotal), 1, 1, 'R');
            }
        }

        $pdf->Ln(5);
    }
    
    /**
     * Add invoice totals to PDF
     */
    private function add_invoice_totals($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        $pdf->SetFont($pdf->getFontFamily(), '', $font_size);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        // Labels based on RTL support
        $subtotal_label = $rtl_support ? 'جمع جزء:' : 'Subtotal:';
        $tax_label = $rtl_support ? 'مالیات:' : 'Tax:';
        $total_label = $rtl_support ? 'جمع کل:' : 'Total:';

        // Subtotal
        if ($rtl_support) {
            $pdf->Cell(35, 6, $this->format_currency($invoice->subtotal), 0, 0, 'L');
            $pdf->Cell(30, 6, $subtotal_label, 0, 1, 'L');
        } else {
            $pdf->Cell(135, 6, '', 0, 0);
            $pdf->Cell(30, 6, $subtotal_label, 0, 0, 'R');
            $pdf->Cell(35, 6, $this->format_currency($invoice->subtotal), 0, 1, 'R');
        }

        // Tax
        if ($invoice->tax_amount > 0) {
            if ($rtl_support) {
                $pdf->Cell(35, 6, $this->format_currency($invoice->tax_amount), 0, 0, 'L');
                $pdf->Cell(30, 6, $tax_label, 0, 1, 'L');
            } else {
                $pdf->Cell(135, 6, '', 0, 0);
                $pdf->Cell(30, 6, $tax_label, 0, 0, 'R');
                $pdf->Cell(35, 6, $this->format_currency($invoice->tax_amount), 0, 1, 'R');
            }
        }

        // Total
        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size + 2);
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

        if ($rtl_support) {
            $pdf->Cell(35, 8, $this->format_currency($invoice->total_amount), 0, 0, 'L');
            $pdf->Cell(30, 8, $total_label, 0, 1, 'L');
        } else {
            $pdf->Cell(135, 8, '', 0, 0);
            $pdf->Cell(30, 8, $total_label, 0, 0, 'R');
            $pdf->Cell(35, 8, $this->format_currency($invoice->total_amount), 0, 1, 'R');
        }

        $pdf->Ln(10);
    }
    
    /**
     * Add invoice footer to PDF
     */
    private function add_invoice_footer($pdf, $invoice) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        $align = $rtl_support ? 'R' : 'L';

        if ($payment_terms) {
            $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
            $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

            $payment_terms_label = $rtl_support ? 'شرایط پرداخت:' : 'Payment Terms:';
            $pdf->Cell(0, 6, $payment_terms_label, 0, 1, $align);

            $pdf->SetFont($pdf->getFontFamily(), '', $font_size - 1);
            $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
            $pdf->MultiCell(0, 5, $payment_terms, 0, $align);
        }

        if ($invoice->notes) {
            $pdf->Ln(5);
            $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
            $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

            $notes_label = $rtl_support ? 'یادداشت‌ها:' : 'Notes:';
            $pdf->Cell(0, 6, $notes_label, 0, 1, $align);

            $pdf->SetFont($pdf->getFontFamily(), '', $font_size - 1);
            $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
            $pdf->MultiCell(0, 5, $invoice->notes, 0, $align);
        }

        // Add a professional footer with company info
        $pdf->Ln(15);
        $pdf->SetFont($pdf->getFontFamily(), 'I', $font_size - 2);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        $footer_text = $rtl_support ?
            'این فاکتور توسط ' . get_option('cfb_company_name', get_bloginfo('name')) . ' تولید شده است.' :
            'This invoice was generated by ' . get_option('cfb_company_name', get_bloginfo('name')) . '.';

        $pdf->Cell(0, 4, $footer_text, 0, 1, 'C');

        $generation_date = $rtl_support ?
            'تاریخ تولید: ' . date('Y/m/d H:i') :
            'Generated on: ' . date('F j, Y \a\t g:i A');

        $pdf->Cell(0, 4, $generation_date, 0, 1, 'C');
    }

    /**
     * Generate classic PDF template
     */
    private function generate_classic_pdf($invoice) {
        // Similar to modern but with different styling
        // Implementation would be similar to generate_modern_pdf but with classic styling
        return $this->generate_modern_pdf($invoice); // Fallback for now
    }

    /**
     * Generate minimal PDF template
     */
    private function generate_minimal_pdf($invoice) {
        // Minimal design implementation
        // Implementation would be similar to generate_modern_pdf but with minimal styling
        return $this->generate_modern_pdf($invoice); // Fallback for now
    }

    /**
     * Generate simple PDF fallback (when TCPDF is not available)
     */
    private function generate_simple_pdf($invoice) {
        // Create a simple HTML-based PDF using WordPress functionality
        $html = $this->generate_invoice_html($invoice);

        // Save as HTML file for now (in production, you'd use a PDF library)
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';

        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }

        $filename = 'invoice-' . $invoice->invoice_number . '.html';
        $filepath = $pdf_dir . $filename;

        file_put_contents($filepath, $html);

        return 'cfb-invoices/' . $filename;
    }

    /**
     * Generate invoice HTML
     */
    private function generate_invoice_html($invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));

        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Invoice <?php echo esc_html($invoice->invoice_number); ?></title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
                .invoice-header { display: flex; justify-content: space-between; margin-bottom: 30px; }
                .company-info { text-align: right; }
                .invoice-title { font-size: 24px; font-weight: bold; margin-bottom: 20px; }
                .invoice-details { margin-bottom: 20px; }
                .customer-info { margin-bottom: 30px; }
                .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .items-table th { background-color: #f5f5f5; font-weight: bold; }
                .totals { text-align: right; margin-bottom: 30px; }
                .total-line { margin: 5px 0; }
                .total-amount { font-size: 18px; font-weight: bold; }
                .notes { margin-top: 30px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="invoice-header">
                <div class="company-info">
                    <h2><?php echo esc_html($company_name); ?></h2>
                    <?php if ($company_address): ?>
                        <p><?php echo nl2br(esc_html($company_address)); ?></p>
                    <?php endif; ?>
                    <?php if ($company_phone): ?>
                        <p>Phone: <?php echo esc_html($company_phone); ?></p>
                    <?php endif; ?>
                    <?php if ($company_email): ?>
                        <p>Email: <?php echo esc_html($company_email); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <div class="invoice-title">INVOICE</div>

            <div class="invoice-details">
                <p><strong>Invoice Number:</strong> <?php echo esc_html($invoice->invoice_number); ?></p>
                <p><strong>Invoice Date:</strong> <?php echo date('F j, Y', strtotime($invoice->created_at)); ?></p>
            </div>

            <div class="customer-info">
                <h3>Bill To:</h3>
                <p><strong><?php echo esc_html($invoice->customer_name); ?></strong></p>
                <?php if ($invoice->customer_email): ?>
                    <p><?php echo esc_html($invoice->customer_email); ?></p>
                <?php endif; ?>
                <?php if ($invoice->customer_phone): ?>
                    <p><?php echo esc_html($invoice->customer_phone); ?></p>
                <?php endif; ?>
                <?php if ($invoice->customer_address): ?>
                    <p><?php echo nl2br(esc_html($invoice->customer_address)); ?></p>
                <?php endif; ?>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($invoice->items)): ?>
                        <?php foreach ($invoice->items as $item): ?>
                            <tr>
                                <td><?php echo esc_html($item->item_name); ?></td>
                                <td><?php echo number_format($item->quantity, 2); ?></td>
                                <td><?php echo $this->format_currency($item->unit_price); ?></td>
                                <td><?php echo $this->format_currency($item->total_price); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td><?php echo esc_html($invoice->form_name . ' Calculation'); ?></td>
                            <td>1</td>
                            <td><?php echo $this->format_currency($invoice->subtotal); ?></td>
                            <td><?php echo $this->format_currency($invoice->subtotal); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <div class="totals">
                <div class="total-line">Subtotal: <?php echo $this->format_currency($invoice->subtotal); ?></div>
                <?php if ($invoice->tax_amount > 0): ?>
                    <div class="total-line">Tax: <?php echo $this->format_currency($invoice->tax_amount); ?></div>
                <?php endif; ?>
                <div class="total-line total-amount">Total: <?php echo $this->format_currency($invoice->total_amount); ?></div>
            </div>

            <?php if ($invoice->notes): ?>
                <div class="notes">
                    <h3>Notes:</h3>
                    <p><?php echo nl2br(esc_html($invoice->notes)); ?></p>
                </div>
            <?php endif; ?>

            <?php
            $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
            if ($payment_terms):
            ?>
                <div class="notes">
                    <h3>Payment Terms:</h3>
                    <p><?php echo nl2br(esc_html($payment_terms)); ?></p>
                </div>
            <?php endif; ?>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Get PDF URL
     */
    private function get_pdf_url($pdf_path) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['baseurl'] . '/' . $pdf_path;
    }

    /**
     * Format currency for display
     */
    private function format_currency($amount) {
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $currency_position = get_option('cfb_currency_position', 'left');
        $decimal_places = get_option('cfb_decimal_places', 2);

        $formatted = number_format($amount, $decimal_places);

        if ($currency_position === 'right') {
            return $formatted . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted;
        }
    }
}
