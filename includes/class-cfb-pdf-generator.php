<?php
/**
 * CFB Calculator PDF Generator
 * Handles PDF invoice generation using TCPDF
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_PDF_Generator {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_cfb_generate_pdf', array($this, 'ajax_generate_pdf'));
        add_action('wp_ajax_nopriv_cfb_generate_pdf', array($this, 'ajax_generate_pdf'));
        
        // Load TCPDF library
        $this->load_tcpdf();
    }
    
    /**
     * Load TCPDF library
     */
    private function load_tcpdf() {
        if (!class_exists('TCPDF')) {
            // Load Composer autoloader first
            $autoloader_path = CFB_CALCULATOR_PLUGIN_PATH . 'vendor/autoload.php';
            if (file_exists($autoloader_path)) {
                require_once $autoloader_path;
            }

            // If still not available, check manual installation paths
            if (!class_exists('TCPDF')) {
                $tcpdf_paths = array(
                    CFB_CALCULATOR_PLUGIN_PATH . 'vendor/tecnickcom/tcpdf/tcpdf.php',
                    CFB_CALCULATOR_PLUGIN_PATH . 'lib/tcpdf/tcpdf.php',
                    ABSPATH . 'wp-content/plugins/tcpdf/tcpdf.php'
                );

                foreach ($tcpdf_paths as $path) {
                    if (file_exists($path)) {
                        require_once $path;
                        break;
                    }
                }
            }

            // If TCPDF is still not found, we'll create a simple fallback
            if (!class_exists('TCPDF')) {
                $this->create_tcpdf_fallback();
            }
        }
    }
    
    /**
     * Create TCPDF fallback for basic PDF generation
     */
    private function create_tcpdf_fallback() {
        // For now, we'll use WordPress's built-in functionality
        // In production, you should install TCPDF properly
    }
    
    /**
     * AJAX generate PDF handler
     */
    public function ajax_generate_pdf() {
        check_ajax_referer('cfb_invoice_nonce', 'nonce');
        
        $invoice_id = intval($_POST['invoice_id']);
        
        if (!$invoice_id) {
            wp_send_json_error(__('Invalid invoice ID', 'cfb-calculator'));
        }
        
        $invoice = $this->get_invoice_data($invoice_id);
        
        if (!$invoice) {
            wp_send_json_error(__('Invoice not found', 'cfb-calculator'));
        }
        
        try {
            $pdf_path = $this->generate_invoice_pdf($invoice);
            
            if ($pdf_path) {
                // Update invoice with PDF path
                global $wpdb;
                $wpdb->update(
                    $wpdb->prefix . 'cfb_invoices',
                    array('pdf_path' => $pdf_path),
                    array('id' => $invoice_id),
                    array('%s'),
                    array('%d')
                );
                
                wp_send_json_success(array(
                    'message' => __('PDF generated successfully', 'cfb-calculator'),
                    'pdf_url' => $this->get_pdf_url($pdf_path)
                ));
            } else {
                wp_send_json_error(__('Failed to generate PDF', 'cfb-calculator'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('PDF generation error: ', 'cfb-calculator') . $e->getMessage());
        }
    }
    
    /**
     * Get invoice data
     */
    private function get_invoice_data($invoice_id) {
        global $wpdb;
        
        $invoice = $wpdb->get_row($wpdb->prepare("
            SELECT i.*, f.name as form_name
            FROM {$wpdb->prefix}cfb_invoices i
            LEFT JOIN {$wpdb->prefix}cfb_forms f ON i.form_id = f.id
            WHERE i.id = %d
        ", $invoice_id));
        
        if ($invoice) {
            // Get invoice items
            $invoice->items = $wpdb->get_results($wpdb->prepare("
                SELECT * FROM {$wpdb->prefix}cfb_invoice_items
                WHERE invoice_id = %d
                ORDER BY sort_order
            ", $invoice_id));
        }
        
        return $invoice;
    }
    
    /**
     * Generate invoice PDF
     */
    public function generate_invoice_pdf($invoice) {
        if (!class_exists('TCPDF')) {
            return $this->generate_simple_pdf($invoice);
        }
        
        // Get PDF template
        $template = get_option('cfb_pdf_template', 'modern');
        
        switch ($template) {
            case 'classic':
                return $this->generate_classic_pdf($invoice);
            case 'minimal':
                return $this->generate_minimal_pdf($invoice);
            case 'modern':
            default:
                return $this->generate_modern_pdf($invoice);
        }
    }
    
    /**
     * Generate modern PDF template
     */
    private function generate_modern_pdf($invoice) {
        // Create new PDF document
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        
        // Set document information
        $pdf->SetCreator('CFB Calculator');
        $pdf->SetAuthor(get_bloginfo('name'));
        $pdf->SetTitle('Invoice ' . $invoice->invoice_number);
        $pdf->SetSubject('Invoice');
        
        // Remove default header/footer
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        
        // Set margins
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetAutoPageBreak(TRUE, 20);
        
        // Add a page
        $pdf->AddPage();
        
        // Set font
        $pdf->SetFont('helvetica', '', 10);
        
        // Company logo and info
        $this->add_company_header($pdf, $invoice);
        
        // Invoice details
        $this->add_invoice_details($pdf, $invoice);
        
        // Customer details
        $this->add_customer_details($pdf, $invoice);
        
        // Invoice items table
        $this->add_invoice_items_table($pdf, $invoice);
        
        // Totals
        $this->add_invoice_totals($pdf, $invoice);
        
        // Footer
        $this->add_invoice_footer($pdf, $invoice);
        
        // Save PDF
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';
        
        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }
        
        $filename = 'invoice-' . $invoice->invoice_number . '.pdf';
        $filepath = $pdf_dir . $filename;
        
        $pdf->Output($filepath, 'F');
        
        return 'cfb-invoices/' . $filename;
    }
    
    /**
     * Add company header to PDF
     */
    private function add_company_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));
        
        // Company logo
        $logo_path = get_option('cfb_company_logo', '');
        if ($logo_path && file_exists($logo_path)) {
            $pdf->Image($logo_path, 20, 20, 40, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
            $pdf->SetY(35);
        }
        
        // Company name
        $pdf->SetFont('helvetica', 'B', 16);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(0, 8, $company_name, 0, 1, 'R');
        
        // Company details
        $pdf->SetFont('helvetica', '', 10);
        $pdf->SetTextColor(102, 102, 102);
        if ($company_address) {
            $pdf->Cell(0, 5, $company_address, 0, 1, 'R');
        }
        if ($company_phone) {
            $pdf->Cell(0, 5, 'Phone: ' . $company_phone, 0, 1, 'R');
        }
        if ($company_email) {
            $pdf->Cell(0, 5, 'Email: ' . $company_email, 0, 1, 'R');
        }
        
        $pdf->Ln(10);
    }
    
    /**
     * Add invoice details to PDF
     */
    private function add_invoice_details($pdf, $invoice) {
        $pdf->SetFont('helvetica', 'B', 24);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(0, 12, 'INVOICE', 0, 1, 'L');
        
        $pdf->SetFont('helvetica', '', 10);
        $pdf->SetTextColor(102, 102, 102);
        
        // Invoice number and date
        $pdf->Cell(40, 6, 'Invoice Number:', 0, 0, 'L');
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(0, 6, $invoice->invoice_number, 0, 1, 'L');
        
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(40, 6, 'Invoice Date:', 0, 0, 'L');
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(0, 6, date('F j, Y', strtotime($invoice->created_at)), 0, 1, 'L');
        
        $pdf->Ln(5);
    }
    
    /**
     * Add customer details to PDF
     */
    private function add_customer_details($pdf, $invoice) {
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(0, 8, 'Bill To:', 0, 1, 'L');
        
        $pdf->SetFont('helvetica', '', 10);
        $pdf->SetTextColor(102, 102, 102);
        
        $pdf->Cell(0, 5, $invoice->customer_name, 0, 1, 'L');
        if ($invoice->customer_email) {
            $pdf->Cell(0, 5, $invoice->customer_email, 0, 1, 'L');
        }
        if ($invoice->customer_phone) {
            $pdf->Cell(0, 5, $invoice->customer_phone, 0, 1, 'L');
        }
        if ($invoice->customer_address) {
            $pdf->MultiCell(0, 5, $invoice->customer_address, 0, 'L');
        }
        
        $pdf->Ln(10);
    }
    
    /**
     * Add invoice items table to PDF
     */
    private function add_invoice_items_table($pdf, $invoice) {
        // Table header
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->SetFillColor(240, 240, 240);
        $pdf->SetTextColor(51, 51, 51);
        
        $pdf->Cell(80, 8, 'Description', 1, 0, 'L', true);
        $pdf->Cell(25, 8, 'Quantity', 1, 0, 'C', true);
        $pdf->Cell(30, 8, 'Unit Price', 1, 0, 'R', true);
        $pdf->Cell(35, 8, 'Total', 1, 1, 'R', true);
        
        // Table content
        $pdf->SetFont('helvetica', '', 9);
        $pdf->SetTextColor(102, 102, 102);
        
        if (!empty($invoice->items)) {
            foreach ($invoice->items as $item) {
                $pdf->Cell(80, 6, $item->item_name, 1, 0, 'L');
                $pdf->Cell(25, 6, number_format($item->quantity, 2), 1, 0, 'C');
                $pdf->Cell(30, 6, $this->format_currency($item->unit_price), 1, 0, 'R');
                $pdf->Cell(35, 6, $this->format_currency($item->total_price), 1, 1, 'R');
            }
        } else {
            // If no items, show the form calculation as a single item
            $pdf->Cell(80, 6, $invoice->form_name . ' Calculation', 1, 0, 'L');
            $pdf->Cell(25, 6, '1', 1, 0, 'C');
            $pdf->Cell(30, 6, $this->format_currency($invoice->subtotal), 1, 0, 'R');
            $pdf->Cell(35, 6, $this->format_currency($invoice->subtotal), 1, 1, 'R');
        }
        
        $pdf->Ln(5);
    }
    
    /**
     * Add invoice totals to PDF
     */
    private function add_invoice_totals($pdf, $invoice) {
        $pdf->SetFont('helvetica', '', 10);
        $pdf->SetTextColor(102, 102, 102);
        
        // Subtotal
        $pdf->Cell(135, 6, '', 0, 0);
        $pdf->Cell(30, 6, 'Subtotal:', 0, 0, 'R');
        $pdf->Cell(35, 6, $this->format_currency($invoice->subtotal), 0, 1, 'R');
        
        // Tax
        if ($invoice->tax_amount > 0) {
            $pdf->Cell(135, 6, '', 0, 0);
            $pdf->Cell(30, 6, 'Tax:', 0, 0, 'R');
            $pdf->Cell(35, 6, $this->format_currency($invoice->tax_amount), 0, 1, 'R');
        }
        
        // Total
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(135, 8, '', 0, 0);
        $pdf->Cell(30, 8, 'Total:', 0, 0, 'R');
        $pdf->Cell(35, 8, $this->format_currency($invoice->total_amount), 0, 1, 'R');
        
        $pdf->Ln(10);
    }
    
    /**
     * Add invoice footer to PDF
     */
    private function add_invoice_footer($pdf, $invoice) {
        if ($invoice->notes) {
            $pdf->SetFont('helvetica', 'B', 10);
            $pdf->SetTextColor(51, 51, 51);
            $pdf->Cell(0, 6, 'Notes:', 0, 1, 'L');
            
            $pdf->SetFont('helvetica', '', 9);
            $pdf->SetTextColor(102, 102, 102);
            $pdf->MultiCell(0, 5, $invoice->notes, 0, 'L');
        }
        
        // Payment terms
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
        if ($payment_terms) {
            $pdf->Ln(5);
            $pdf->SetFont('helvetica', 'B', 10);
            $pdf->SetTextColor(51, 51, 51);
            $pdf->Cell(0, 6, 'Payment Terms:', 0, 1, 'L');
            
            $pdf->SetFont('helvetica', '', 9);
            $pdf->SetTextColor(102, 102, 102);
            $pdf->MultiCell(0, 5, $payment_terms, 0, 'L');
        }
    }

    /**
     * Generate classic PDF template
     */
    private function generate_classic_pdf($invoice) {
        // Similar to modern but with different styling
        // Implementation would be similar to generate_modern_pdf but with classic styling
        return $this->generate_modern_pdf($invoice); // Fallback for now
    }

    /**
     * Generate minimal PDF template
     */
    private function generate_minimal_pdf($invoice) {
        // Minimal design implementation
        // Implementation would be similar to generate_modern_pdf but with minimal styling
        return $this->generate_modern_pdf($invoice); // Fallback for now
    }

    /**
     * Generate simple PDF fallback (when TCPDF is not available)
     */
    private function generate_simple_pdf($invoice) {
        // Create a simple HTML-based PDF using WordPress functionality
        $html = $this->generate_invoice_html($invoice);

        // Save as HTML file for now (in production, you'd use a PDF library)
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';

        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }

        $filename = 'invoice-' . $invoice->invoice_number . '.html';
        $filepath = $pdf_dir . $filename;

        file_put_contents($filepath, $html);

        return 'cfb-invoices/' . $filename;
    }

    /**
     * Generate invoice HTML
     */
    private function generate_invoice_html($invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));

        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Invoice <?php echo esc_html($invoice->invoice_number); ?></title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
                .invoice-header { display: flex; justify-content: space-between; margin-bottom: 30px; }
                .company-info { text-align: right; }
                .invoice-title { font-size: 24px; font-weight: bold; margin-bottom: 20px; }
                .invoice-details { margin-bottom: 20px; }
                .customer-info { margin-bottom: 30px; }
                .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .items-table th { background-color: #f5f5f5; font-weight: bold; }
                .totals { text-align: right; margin-bottom: 30px; }
                .total-line { margin: 5px 0; }
                .total-amount { font-size: 18px; font-weight: bold; }
                .notes { margin-top: 30px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="invoice-header">
                <div class="company-info">
                    <h2><?php echo esc_html($company_name); ?></h2>
                    <?php if ($company_address): ?>
                        <p><?php echo nl2br(esc_html($company_address)); ?></p>
                    <?php endif; ?>
                    <?php if ($company_phone): ?>
                        <p>Phone: <?php echo esc_html($company_phone); ?></p>
                    <?php endif; ?>
                    <?php if ($company_email): ?>
                        <p>Email: <?php echo esc_html($company_email); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <div class="invoice-title">INVOICE</div>

            <div class="invoice-details">
                <p><strong>Invoice Number:</strong> <?php echo esc_html($invoice->invoice_number); ?></p>
                <p><strong>Invoice Date:</strong> <?php echo date('F j, Y', strtotime($invoice->created_at)); ?></p>
            </div>

            <div class="customer-info">
                <h3>Bill To:</h3>
                <p><strong><?php echo esc_html($invoice->customer_name); ?></strong></p>
                <?php if ($invoice->customer_email): ?>
                    <p><?php echo esc_html($invoice->customer_email); ?></p>
                <?php endif; ?>
                <?php if ($invoice->customer_phone): ?>
                    <p><?php echo esc_html($invoice->customer_phone); ?></p>
                <?php endif; ?>
                <?php if ($invoice->customer_address): ?>
                    <p><?php echo nl2br(esc_html($invoice->customer_address)); ?></p>
                <?php endif; ?>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($invoice->items)): ?>
                        <?php foreach ($invoice->items as $item): ?>
                            <tr>
                                <td><?php echo esc_html($item->item_name); ?></td>
                                <td><?php echo number_format($item->quantity, 2); ?></td>
                                <td><?php echo $this->format_currency($item->unit_price); ?></td>
                                <td><?php echo $this->format_currency($item->total_price); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td><?php echo esc_html($invoice->form_name . ' Calculation'); ?></td>
                            <td>1</td>
                            <td><?php echo $this->format_currency($invoice->subtotal); ?></td>
                            <td><?php echo $this->format_currency($invoice->subtotal); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <div class="totals">
                <div class="total-line">Subtotal: <?php echo $this->format_currency($invoice->subtotal); ?></div>
                <?php if ($invoice->tax_amount > 0): ?>
                    <div class="total-line">Tax: <?php echo $this->format_currency($invoice->tax_amount); ?></div>
                <?php endif; ?>
                <div class="total-line total-amount">Total: <?php echo $this->format_currency($invoice->total_amount); ?></div>
            </div>

            <?php if ($invoice->notes): ?>
                <div class="notes">
                    <h3>Notes:</h3>
                    <p><?php echo nl2br(esc_html($invoice->notes)); ?></p>
                </div>
            <?php endif; ?>

            <?php
            $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
            if ($payment_terms):
            ?>
                <div class="notes">
                    <h3>Payment Terms:</h3>
                    <p><?php echo nl2br(esc_html($payment_terms)); ?></p>
                </div>
            <?php endif; ?>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Get PDF URL
     */
    private function get_pdf_url($pdf_path) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['baseurl'] . '/' . $pdf_path;
    }

    /**
     * Format currency for display
     */
    private function format_currency($amount) {
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $currency_position = get_option('cfb_currency_position', 'left');
        $decimal_places = get_option('cfb_decimal_places', 2);

        $formatted = number_format($amount, $decimal_places);

        if ($currency_position === 'right') {
            return $formatted . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted;
        }
    }
}
