/* CFB Calculator Frontend Styles */

/* Reset and Base Styles */
.cfb-calculator-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.cfb-calculator-wrapper:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* RTL Support */
.cfb-calculator-wrapper[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-field-label {
    text-align: right;
}

/* Header */
.cfb-calculator-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    margin: 0;
    padding: 24px 30px;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
}

.cfb-calculator-description {
    padding: 20px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    color: #6c757d;
    text-align: center;
}

/* Form Styles */
.cfb-calculator-form {
    padding: 30px;
}

.cfb-form-fields {
    display: grid;
    gap: 24px;
    margin-bottom: 30px;
}

/* Field Styles */
.cfb-field {
    position: relative;
    transition: all 0.3s ease;
}

.cfb-field.cfb-field-hidden {
    display: none;
}

.cfb-field-label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 14px;
}

.cfb-required {
    color: #e74c3c;
    margin-left: 4px;
}

.cfb-field-input {
    position: relative;
}

/* Input Styles */
.cfb-text-input,
.cfb-number-input,
.cfb-select-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fff;
    box-sizing: border-box;
}

.cfb-text-input:focus,
.cfb-number-input:focus,
.cfb-select-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Slider Styles */
.cfb-slider-container {
    padding: 10px 0;
}

.cfb-slider-input {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.cfb-slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.cfb-slider-input::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.cfb-slider-input::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.cfb-slider-value {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
    color: #6c757d;
}

.cfb-slider-current {
    font-weight: 600;
    color: #667eea;
    font-size: 16px;
}

/* Radio and Checkbox Styles */
.cfb-radio-group,
.cfb-checkbox-group {
    display: grid;
    gap: 12px;
}

.cfb-radio-option,
.cfb-checkbox-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
}

.cfb-radio-option:hover,
.cfb-checkbox-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.cfb-radio-option input,
.cfb-checkbox-option input {
    margin-right: 12px;
    margin-left: 0;
    transform: scale(1.2);
    accent-color: #667eea;
}

.cfb-radio-option input:checked + .cfb-radio-label,
.cfb-checkbox-option input:checked + .cfb-checkbox-label {
    color: #667eea;
    font-weight: 600;
}

.cfb-option-price {
    margin-left: auto;
    color: #28a745;
    font-weight: 600;
}

/* RTL adjustments for radio/checkbox */
.cfb-calculator-wrapper[dir="rtl"] .cfb-radio-option input,
.cfb-calculator-wrapper[dir="rtl"] .cfb-checkbox-option input {
    margin-right: 0;
    margin-left: 12px;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-option-price {
    margin-left: 0;
    margin-right: auto;
}

/* Field Description */
.cfb-field-description {
    margin-top: 6px;
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

/* Calculation Results */
.cfb-calculation-results {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #e9ecef;
}

.cfb-subtotals h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.cfb-subtotal-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.cfb-subtotal-line:last-child {
    border-bottom: none;
}

.cfb-subtotal-label {
    color: #495057;
    font-size: 14px;
}

.cfb-subtotal-value {
    font-weight: 600;
    color: #28a745;
    font-size: 14px;
}

.cfb-total-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #667eea;
}

.cfb-total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.cfb-total-label {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

.cfb-total-value {
    font-size: 24px;
    font-weight: 700;
    color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Actions */
.cfb-form-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
}

.cfb-calculate-btn,
.cfb-reset-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cfb-calculate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.cfb-calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.cfb-reset-btn {
    background: #6c757d;
    color: #fff;
}

.cfb-reset-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Loading State */
.cfb-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 20px;
    color: #667eea;
    font-weight: 600;
}

.cfb-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: cfb-spin 1s linear infinite;
}

@keyframes cfb-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.cfb-error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
    margin-top: 16px;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cfb-calculator-wrapper {
        margin: 0 16px;
        border-radius: 8px;
    }
    
    .cfb-calculator-form {
        padding: 20px;
    }
    
    .cfb-calculator-title {
        padding: 20px;
        font-size: 20px;
    }
    
    .cfb-form-actions {
        flex-direction: column;
    }
    
    .cfb-calculate-btn,
    .cfb-reset-btn {
        width: 100%;
    }
    
    .cfb-total-value {
        font-size: 20px;
    }
    
    .cfb-radio-group,
    .cfb-checkbox-group {
        gap: 8px;
    }
    
    .cfb-radio-option,
    .cfb-checkbox-option {
        padding: 10px 12px;
    }
}

/* Animation Classes */
.cfb-fade-in {
    animation: cfb-fadeIn 0.5s ease-in-out;
}

.cfb-slide-up {
    animation: cfb-slideUp 0.5s ease-in-out;
}

@keyframes cfb-fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes cfb-slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .cfb-calculator-wrapper {
        border: 2px solid #000;
    }
    
    .cfb-text-input,
    .cfb-number-input,
    .cfb-select-input {
        border: 2px solid #000;
    }
    
    .cfb-calculate-btn {
        background: #000;
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .cfb-calculator-wrapper,
    .cfb-field,
    .cfb-text-input,
    .cfb-number-input,
    .cfb-select-input,
    .cfb-slider-input::-webkit-slider-thumb,
    .cfb-radio-option,
    .cfb-checkbox-option,
    .cfb-calculate-btn,
    .cfb-reset-btn {
        transition: none;
    }
    
    .cfb-spinner {
        animation: none;
    }
    
    .cfb-fade-in,
    .cfb-slide-up {
        animation: none;
    }
}

/* Hide Results Initially */
.cfb-results-hidden .cfb-calculation-field,
.cfb-results-hidden .cfb-total-section,
.cfb-results-hidden .cfb-results-section,
.cfb-results-hidden .cfb-subtotal-section,
.cfb-results-hidden .cfb-calculation-results {
    display: none !important;
}

/* Show results with animation when revealed */
.cfb-calculation-field,
.cfb-total-section,
.cfb-results-section,
.cfb-subtotal-section,
.cfb-calculation-results {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-calculation-field,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-total-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-results-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-subtotal-section,
.cfb-calculator-wrapper:not(.cfb-results-hidden) .cfb-calculation-results {
    opacity: 1;
    transform: translateY(0);
}

/* Conditional field hiding */
.cfb-field-hidden {
    display: none !important;
}

.cfb-field.cfb-fade-in {
    animation: cfbFadeIn 0.3s ease;
}

@keyframes cfbFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
